"""
Репозиторий для работы с предметами
"""
from typing import List, Optional
from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from ..models import Subject
from ..database import get_db_session


class SubjectRepository:
    """Репозиторий для работы с предметами"""
    
    @staticmethod
    async def get_all() -> List[Subject]:
        """Получить все предметы"""
        async with get_db_session() as session:
            result = await session.execute(
                select(Subject)
                .options(selectinload(Subject.course))
                .order_by(Subject.name)
            )
            return list(result.scalars().all())
    
    @staticmethod
    async def get_by_id(subject_id: int) -> Optional[Subject]:
        """Получить предмет по ID"""
        async with get_db_session() as session:
            result = await session.execute(
                select(Subject)
                .options(selectinload(Subject.course))
                .where(Subject.id == subject_id)
            )
            return result.scalar_one_or_none()
    
    @staticmethod
    async def get_by_course(course_id: int) -> List[Subject]:
        """Получить предметы по курсу"""
        async with get_db_session() as session:
            result = await session.execute(
                select(Subject)
                .options(selectinload(Subject.course))
                .where(Subject.course_id == course_id)
                .order_by(Subject.name)
            )
            return list(result.scalars().all())
    
    @staticmethod
    async def create(name: str, course_id: int = None) -> Subject:
        """Создать новый предмет"""
        async with get_db_session() as session:
            subject = Subject(name=name, course_id=course_id)
            session.add(subject)
            await session.commit()
            await session.refresh(subject)
            return subject
    
    @staticmethod
    async def delete(subject_id: int) -> bool:
        """Удалить предмет"""
        async with get_db_session() as session:
            result = await session.execute(delete(Subject).where(Subject.id == subject_id))
            await session.commit()
            return result.rowcount > 0
