"""
Модели SQLAlchemy для базы данных
"""
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy import Column, Integer, String, Text, DateTime
from sqlalchemy.sql import func


# Базовый класс для моделей
class Base(DeclarativeBase):
    pass


# Модель курса
class Course(Base):
    __tablename__ = 'courses'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    created_at = Column(DateTime, server_default=func.now())
