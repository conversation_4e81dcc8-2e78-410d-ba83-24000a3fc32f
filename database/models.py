"""
Модели SQLAlchemy для базы данных
"""
from sqlalchemy.orm import DeclarativeBase, relationship
from sqlalchemy import Column, Integer, BigInteger, String, Text, DateTime, ForeignKey
from sqlalchemy.sql import func


# Базовый класс для моделей
class Base(DeclarativeBase):
    pass


# Модель пользователя
class User(Base):
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True)
    telegram_id = Column(Integer, unique=True, nullable=False)
    name = Column(String(255), nullable=False)
    role = Column(String(50), default='student')
    created_at = Column(DateTime, server_default=func.now())


# Модель курса
class Course(Base):
    __tablename__ = 'courses'

    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    created_at = Column(DateTime, server_default=func.now())

    # Связь с предметами
    subjects = relationship("Subject", back_populates="course")


# Модель предмета
class Subject(Base):
    __tablename__ = 'subjects'

    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    course_id = Column(Integer, ForeignKey('courses.id'))
    created_at = Column(DateTime, server_default=func.now())

    # Связь с курсом
    course = relationship("Course", back_populates="subjects")
