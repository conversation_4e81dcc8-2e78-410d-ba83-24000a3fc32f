from typing import Callable, Dict, Any, Awaitable
from aiogram import BaseMiddleware
from aiogram.types import Message, CallbackQuery
from database import UserRepository

class RoleMiddleware(BaseMiddleware):
    """Middleware для определения роли пользователя"""

    async def __call__(
        self,
        handler: Callable[[Message | CallbackQuery, Dict[str, Any]], Awaitable[Any]],
        event: Message | CallbackQuery,
        data: Dict[str, Any]
    ) -> Any:
        # Получаем user_id и имя пользователя
        user_id = event.from_user.id
        user_name = event.from_user.full_name or event.from_user.username or f"User_{user_id}"

        print(f"User: {user_name} (ID: {user_id})")

        # Получаем пользователя из базы данных
        try:
            user = await UserRepository.get_by_telegram_id(user_id)

            if user:
                # Пользователь найден в базе
                role = user.role
                print(f"✅ Роль из БД: {role}")
            else:
                # Пользователь не найден - создаем нового с ролью student
                print(f"⚠️ Пользователь не найден в БД, создаем нового")

                # Проверяем, является ли пользователь админом по ID (для первоначальной настройки)
                admin_ids = [955518340]  # Андрей Климов

                if user_id in admin_ids:
                    role = "admin"
                    print(f"🔑 Создан админ: {user_name}")
                else:
                    role = "student"
                    print(f"👤 Создан студент: {user_name}")

                # Создаем пользователя в базе
                try:
                    new_user = await UserRepository.create(user_id, user_name, role)
                    print(f"✅ Пользователь создан в БД: {new_user.name} ({new_user.role})")
                except Exception as create_error:
                    print(f"❌ Ошибка создания пользователя: {create_error}")

        except Exception as e:
            print(f"❌ Ошибка работы с БД: {e}")
            # Fallback: определяем роль по ID
            admin_ids = [955518340]  # Андрей Климов
            role = "admin" if user_id in admin_ids else "student"
            print(f"🔄 Fallback роль: {role}")

        # Добавляем роль и информацию о пользователе в данные события
        data["user_role"] = role
        data["user_id"] = user_id
        data["user_name"] = user_name

        # Продолжаем обработку события
        return await handler(event, data)