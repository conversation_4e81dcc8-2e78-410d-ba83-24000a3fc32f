"""
Скрипт для добавления начальных данных в базу данных
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import (
    init_database, 
    UserRepository, 
    CourseRepository, 
    SubjectRepository
)


async def add_initial_data():
    """Добавить начальные данные"""
    print("🚀 Инициализация базы данных...")
    await init_database()
    
    print("📚 Добавление курсов...")
    # Добавляем курсы
    try:
        course_ent = await CourseRepository.create("ЕНТ")
        course_it = await CourseRepository.create("IT")
        print(f"✅ Курс '{course_ent.name}' создан (ID: {course_ent.id})")
        print(f"✅ Курс '{course_it.name}' создан (ID: {course_it.id})")
    except Exception as e:
        print(f"⚠️ Курсы уже существуют или ошибка: {e}")
        # Получаем существующие курсы
        courses = await CourseRepository.get_all()
        course_ent = next((c for c in courses if c.name == "ЕНТ"), None)
        course_it = next((c for c in courses if c.name == "IT"), None)
    
    print("📖 Добавление предметов...")
    # Добавляем предметы для ЕНТ
    if course_ent:
        ent_subjects = [
            "Математика",
            "Физика", 
            "История Казахстана",
            "Химия",
            "Биология"
        ]
        
        for subject_name in ent_subjects:
            try:
                subject = await SubjectRepository.create(subject_name, course_ent.id)
                print(f"✅ Предмет '{subject.name}' создан для курса ЕНТ")
            except Exception as e:
                print(f"⚠️ Предмет '{subject_name}' уже существует или ошибка: {e}")
    
    # Добавляем предметы для IT
    if course_it:
        it_subjects = [
            "Python",
            "JavaScript",
            "Java"
        ]
        
        for subject_name in it_subjects:
            try:
                subject = await SubjectRepository.create(subject_name, course_it.id)
                print(f"✅ Предмет '{subject.name}' создан для курса IT")
            except Exception as e:
                print(f"⚠️ Предмет '{subject_name}' уже существует или ошибка: {e}")
    
    print("👥 Добавление тестовых пользователей...")
    # Добавляем тестовых пользователей
    test_users = [
        (123456789, "Админ Тестовый", "admin"),
        (987654321, "Менеджер Тестовый", "manager"),
        (111222333, "Куратор Тестовый", "curator"),
        (444555666, "Преподаватель Тестовый", "teacher"),
        (777888999, "Студент Тестовый", "student"),
    ]
    
    for telegram_id, name, role in test_users:
        try:
            user = await UserRepository.create(telegram_id, name, role)
            print(f"✅ Пользователь '{user.name}' ({user.role}) создан (Telegram ID: {user.telegram_id})")
        except Exception as e:
            print(f"⚠️ Пользователь с Telegram ID {telegram_id} уже существует или ошибка: {e}")
    
    print("🎉 Начальные данные добавлены!")


if __name__ == "__main__":
    asyncio.run(add_initial_data())
